using System.Threading.Tasks;
using FluentAssertions;
using StackExchange.Redis;
using SmaTrendFollower.Tests.Core.TestHelpers;
using Xunit;

namespace SmaTrendFollower.Tests.Core;

public class ExampleTests
{
    private readonly IDatabase _db = InMemoryRedis.Create();

    [Fact]
    public async Task Redis_Helper_Should_Clear_Database()
    {
        // Arrange
        await _db.StringSetAsync("foo", "bar");
        (await _db.KeyExistsAsync("foo")).Should().BeTrue();

        // Act – flush DB via helper
        await _db.ExecuteAsync("FLUSHDB");

        // Assert
        (await _db.KeyExistsAsync("foo")).Should().BeFalse();
    }
}
