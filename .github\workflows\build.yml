name: build-test

on: [push, pull_request]

jobs:
  ci:
    runs-on: ubuntu-latest   # later switch to [self-hosted, local]
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-dotnet@v4
      with: 
        dotnet-version: '8.0.x'
    - run: dotnet restore
    - run: dotnet test SmaTrendFollower.Tests.Core/SmaTrendFollower.Tests.Core.csproj --configuration Release --filter "Category!=Integration" --collect:"XPlat Code Coverage"
    - run: dotnet publish SmaTrendFollower.Console -c Release -o publish
    - uses: actions/upload-artifact@v4
      with: 
        name: bot-build
        path: publish
