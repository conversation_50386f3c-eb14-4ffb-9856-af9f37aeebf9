using StackExchange.Redis;
using NSubstitute;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Tests.Core.TestHelpers;

public static class InMemoryRedis
{
    public static IDatabase Create()
    {
        return CreateMockDatabase();
    }

    private static IDatabase CreateMockDatabase()
    {
        var mockDatabase = Substitute.For<IDatabase>();
        var storage = new ConcurrentDictionary<string, (RedisValue value, DateTime? expiry)>();

        // Mock StringSetAsync
        mockDatabase.StringSetAsync(Arg.Any<RedisKey>(), Arg.Any<RedisValue>(), Arg.Any<TimeSpan?>(), Arg.Any<When>(), Arg.Any<CommandFlags>())
            .Returns(callInfo =>
            {
                var key = callInfo.ArgAt<RedisKey>(0).ToString();
                var value = callInfo.ArgAt<RedisValue>(1);
                var expiry = callInfo.ArgAt<TimeSpan?>(2);

                DateTime? expiryTime = expiry.HasValue ? DateTime.UtcNow.Add(expiry.Value) : null;
                storage[key] = (value, expiryTime);
                return Task.FromResult(true);
            });

        // Mock StringGetAsync
        mockDatabase.StringGetAsync(Arg.Any<RedisKey>(), Arg.Any<CommandFlags>())
            .Returns(callInfo =>
            {
                var key = callInfo.ArgAt<RedisKey>(0).ToString();
                if (storage.TryGetValue(key, out var item))
                {
                    if (item.expiry.HasValue && DateTime.UtcNow > item.expiry.Value)
                    {
                        storage.TryRemove(key, out _);
                        return Task.FromResult(RedisValue.Null);
                    }
                    return Task.FromResult(item.value);
                }
                return Task.FromResult(RedisValue.Null);
            });

        // Mock KeyExistsAsync
        mockDatabase.KeyExistsAsync(Arg.Any<RedisKey>(), Arg.Any<CommandFlags>())
            .Returns(callInfo =>
            {
                var key = callInfo.ArgAt<RedisKey>(0).ToString();
                if (storage.TryGetValue(key, out var item))
                {
                    if (item.expiry.HasValue && DateTime.UtcNow > item.expiry.Value)
                    {
                        storage.TryRemove(key, out _);
                        return Task.FromResult(false);
                    }
                    return Task.FromResult(true);
                }
                return Task.FromResult(false);
            });

        // Mock ExecuteAsync for FLUSHDB
        mockDatabase.ExecuteAsync(Arg.Any<string>(), Arg.Any<object[]>(), Arg.Any<CommandFlags>())
            .Returns(callInfo =>
            {
                var command = callInfo.ArgAt<string>(0);
                if (command.Equals("FLUSHDB", StringComparison.OrdinalIgnoreCase))
                {
                    storage.Clear();
                }
                return Task.FromResult(RedisResult.Create((RedisValue)"OK"));
            });

        return mockDatabase;
    }
}


