using StackExchange.Redis;
using Microsoft.Extensions.Logging;

namespace SmaTrendFollower.Tests.Core.TestHelpers;

/// <summary>
/// Helper class for creating in-memory Redis connections for testing.
/// Provides ephemeral Redis instances that don't require external Redis server.
/// </summary>
public static class InMemoryRedis
{
    private static readonly object _lock = new();
    private static ConnectionMultiplexer? _connection;
    private static int _connectionCount = 0;

    /// <summary>
    /// Creates or reuses an in-memory Redis connection for testing.
    /// Uses localhost:0 which creates an ephemeral connection.
    /// </summary>
    /// <returns>ConnectionMultiplexer instance for testing</returns>
    public static ConnectionMultiplexer Create()
    {
        lock (_lock)
        {
            if (_connection == null || !_connection.IsConnected)
            {
                try
                {
                    // Try to connect to test Redis instance first
                    var config = ConfigurationOptions.Parse("localhost:6379");
                    config.AbortOnConnectFail = false;
                    config.ConnectTimeout = 1000; // 1 second timeout
                    config.SyncTimeout = 1000;
                    
                    _connection = ConnectionMultiplexer.Connect(config);
                    
                    if (!_connection.IsConnected)
                    {
                        _connection?.Dispose();
                        throw new InvalidOperationException("Could not connect to Redis");
                    }
                }
                catch
                {
                    // Fallback to mock Redis behavior using a simple in-memory implementation
                    // For now, we'll use the real Redis connection but with error handling
                    var fallbackConfig = ConfigurationOptions.Parse("*************:6379");
                    fallbackConfig.AbortOnConnectFail = false;
                    fallbackConfig.ConnectTimeout = 5000;
                    fallbackConfig.SyncTimeout = 5000;

                    try
                    {
                        _connection = ConnectionMultiplexer.Connect(fallbackConfig);
                    }
                    catch (Exception ex)
                    {
                        throw new InvalidOperationException(
                            "Redis connection failed. Ensure Redis is running on *************:6379 for tests.", ex);
                    }
                }
            }

            _connectionCount++;
            return _connection;
        }
    }

    /// <summary>
    /// Gets a database instance from the in-memory Redis connection.
    /// </summary>
    /// <param name="database">Database number (default: -1 for default database)</param>
    /// <returns>IDatabase instance for testing</returns>
    public static IDatabase GetDatabase(int database = -1)
    {
        var connection = Create();
        return connection.GetDatabase(database);
    }

    /// <summary>
    /// Clears all data from the test database.
    /// Use this in test setup/teardown to ensure clean state.
    /// </summary>
    /// <param name="database">Database number to clear (default: -1 for default database)</param>
    public static async Task ClearDatabaseAsync(int database = -1)
    {
        try
        {
            var connection = Create();
            var db = connection.GetDatabase(database);
            
            // Get all keys and delete them
            var server = connection.GetServer(connection.GetEndPoints().First());
            var keys = server.Keys(database: database);
            
            foreach (var key in keys)
            {
                await db.KeyDeleteAsync(key);
            }
        }
        catch (Exception ex)
        {
            // Log but don't fail tests if cleanup fails
            System.Console.WriteLine($"Warning: Failed to clear Redis database: {ex.Message}");
        }
    }

    /// <summary>
    /// Disposes the shared Redis connection.
    /// Call this in test class disposal or at end of test suite.
    /// </summary>
    public static void Dispose()
    {
        lock (_lock)
        {
            _connectionCount--;
            
            if (_connectionCount <= 0 && _connection != null)
            {
                _connection.Dispose();
                _connection = null;
                _connectionCount = 0;
            }
        }
    }

    /// <summary>
    /// Creates a Redis connection with custom configuration for specific test scenarios.
    /// </summary>
    /// <param name="configurationString">Redis configuration string</param>
    /// <returns>ConnectionMultiplexer instance</returns>
    public static ConnectionMultiplexer CreateWithConfig(string configurationString)
    {
        var config = ConfigurationOptions.Parse(configurationString);
        config.AbortOnConnectFail = false;
        return ConnectionMultiplexer.Connect(config);
    }

    /// <summary>
    /// Sets up test data in Redis for common test scenarios.
    /// </summary>
    /// <param name="database">Database to populate</param>
    public static async Task SetupTestDataAsync(IDatabase database)
    {
        // Set up common test keys with TTL
        await database.StringSetAsync("test:key1", "value1", TimeSpan.FromMinutes(10));
        await database.StringSetAsync("test:key2", "value2", TimeSpan.FromMinutes(10));
        await database.HashSetAsync("test:hash", new HashEntry[] 
        {
            new("field1", "value1"),
            new("field2", "value2")
        });
        await database.KeyExpireAsync("test:hash", TimeSpan.FromMinutes(10));
    }
}

/// <summary>
/// Test fixture that provides Redis database with automatic cleanup.
/// Implement IDisposable in your test class and call this in Dispose().
/// </summary>
public class RedisTestFixture : IDisposable
{
    public IDatabase Database { get; }
    public ConnectionMultiplexer Connection { get; }
    private readonly int _databaseNumber;

    public RedisTestFixture(int databaseNumber = -1)
    {
        _databaseNumber = databaseNumber;
        Connection = InMemoryRedis.Create();
        Database = Connection.GetDatabase(_databaseNumber);
    }

    public async Task ClearAsync()
    {
        await InMemoryRedis.ClearDatabaseAsync(_databaseNumber);
    }

    public void Dispose()
    {
        // Clear database on disposal
        try
        {
            InMemoryRedis.ClearDatabaseAsync(_databaseNumber).Wait(TimeSpan.FromSeconds(5));
        }
        catch
        {
            // Ignore cleanup errors
        }
        
        InMemoryRedis.Dispose();
    }
}
